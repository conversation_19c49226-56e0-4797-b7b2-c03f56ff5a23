---
title: "Execution Instructions: Milestone M0.1 — Knowledge-Graph Bootstrap"
milestone: "milestone-M0.1"
agent: "augment"
generated: "2025-06-01"
status: "Ready for Execution"
---

# Milestone M0.1 — Knowledge-Graph Bootstrap - Execution Instructions for Augment

## 🎯 Milestone Overview

**Goal**: Parse existing MDX specs into a JSON-LD + YAML graph; emit CLI tools every agent can run.  
**Milestone ID**: milestone-M0.1
**Target Agent**: augment
**Generated**: 2025-06-01

## 📋 Pre-Execution Checklist

Before starting, ensure you have:

- [ ] Read and understood the agent-specific rules for augment
- [ ] Verified your development environment is properly set up
- [ ] Confirmed you have access to all required tools and dependencies
- [ ] Reviewed the milestone specification thoroughly

### Key Rules for Augment Agents:

- **Use codebase-retrieval** to understand requirements and context
- **Use view** to examine milestone specification thoroughly
- **Create requirement checklist** using templates
- **Plan implementation approach** based on existing patterns
- **Use codebase-retrieval** to find relevant code patterns

## 🔨 Execution Steps

Execute the following steps in order. Each step includes validation criteria that must be met before proceeding to the next step.

### Step 1: `pnpm run build-kg` scans all `docs/tech-specs/**/*.mdx` and writes `kg.jsonld` and `kg.yaml` in repo root.

**Description**: No detailed description available

**Validation Criteria**:
- [ ] Package.json contains required dependencies
- [ ] Package installs without errors
- [ ] Build command succeeds

**Commands to verify**:
```bash
# Add specific validation commands here based on step type
# Example: ls -la, pnpm build, npm test, etc.
```

---

### Step 2: CLI supports `--dry-run` (prints summary, no file write).

**Description**: No detailed description available

**Validation Criteria**:
- [ ] TypeScript compilation succeeds
- [ ] No TypeScript errors

**Commands to verify**:
```bash
# Add specific validation commands here based on step type
# Example: ls -la, pnpm build, npm test, etc.
```

---

### Step 3: CI job `graph-build` passes on push & PR.

**Description**: No detailed description available

**Validation Criteria**:
- [ ] Step completed as described
- [ ] No errors in console/logs

**Commands to verify**:
```bash
# Add specific validation commands here based on step type
# Example: ls -la, pnpm build, npm test, etc.
```

---

### Step 4: Every MDX spec contains a resolvable `@id` in the graph.

**Description**: No detailed description available

**Validation Criteria**:
- [ ] Step completed as described
- [ ] No errors in console/logs

**Commands to verify**:
```bash
# Add specific validation commands here based on step type
# Example: ls -la, pnpm build, npm test, etc.
```

---

### Step 5: Spec passes `spec-lint` and dry-run gates.

**Description**: ---

**Validation Criteria**:
- [ ] Step completed as described
- [ ] No errors in console/logs

**Commands to verify**:
```bash
# Add specific validation commands here based on step type
# Example: ls -la, pnpm build, npm test, etc.
```

---

### Step 6: [ ] **SC-1** `pnpm run build-kg -- --dry-run docs/tech-specs` exits 0.

**Description**: - [ ] **SC-2** Running without `--dry-run` writes both graph files.
- [ ] **SC-3** CI `graph.yml` job passes on PR & push.
- [ ] **SC-4** `kg.yaml` shows at least: one milestone node (M0), one component node, one implements edge.
- [ ] **SC-5** Spec passes checklist lint:
  ```bash
  node scripts/spec-lint.mjs docs/tech-specs/milestones/milestone-M0.1.mdx
  ```
- [ ] **SC-6** Agent dry-run passes:
  ```bash
  pnpm run agent:dry-run --spec docs/tech-specs/milestones/milestone-M0.1.mdx
  ```
---

**Validation Criteria**:
- [ ] Package.json contains required dependencies
- [ ] Package installs without errors
- [ ] Build command succeeds
- [ ] TypeScript compilation succeeds
- [ ] No TypeScript errors

**Commands to verify**:
```bash
# Add specific validation commands here based on step type
# Example: ls -la, pnpm build, npm test, etc.
```

---

### Step 7: [ ] **SC-2** Running without `--dry-run` writes both graph files.

**Description**: - [ ] **SC-3** CI `graph.yml` job passes on PR & push.
- [ ] **SC-4** `kg.yaml` shows at least: one milestone node (M0), one component node, one implements edge.
- [ ] **SC-5** Spec passes checklist lint:
  ```bash
  node scripts/spec-lint.mjs docs/tech-specs/milestones/milestone-M0.1.mdx
  ```
- [ ] **SC-6** Agent dry-run passes:
  ```bash
  pnpm run agent:dry-run --spec docs/tech-specs/milestones/milestone-M0.1.mdx
  ```
---

**Validation Criteria**:
- [ ] Package.json contains required dependencies
- [ ] Package installs without errors
- [ ] Build command succeeds
- [ ] TypeScript compilation succeeds
- [ ] No TypeScript errors

**Commands to verify**:
```bash
# Add specific validation commands here based on step type
# Example: ls -la, pnpm build, npm test, etc.
```

---

### Step 8: [ ] **SC-3** CI `graph.yml` job passes on PR & push.

**Description**: - [ ] **SC-4** `kg.yaml` shows at least: one milestone node (M0), one component node, one implements edge.
- [ ] **SC-5** Spec passes checklist lint:
  ```bash
  node scripts/spec-lint.mjs docs/tech-specs/milestones/milestone-M0.1.mdx
  ```
- [ ] **SC-6** Agent dry-run passes:
  ```bash
  pnpm run agent:dry-run --spec docs/tech-specs/milestones/milestone-M0.1.mdx
  ```
---

**Validation Criteria**:
- [ ] Package.json contains required dependencies
- [ ] Package installs without errors
- [ ] Build command succeeds
- [ ] TypeScript compilation succeeds
- [ ] No TypeScript errors

**Commands to verify**:
```bash
# Add specific validation commands here based on step type
# Example: ls -la, pnpm build, npm test, etc.
```

---

### Step 9: [ ] **SC-4** `kg.yaml` shows at least: one milestone node (M0), one component node, one implements edge.

**Description**: - [ ] **SC-5** Spec passes checklist lint:
  ```bash
  node scripts/spec-lint.mjs docs/tech-specs/milestones/milestone-M0.1.mdx
  ```
- [ ] **SC-6** Agent dry-run passes:
  ```bash
  pnpm run agent:dry-run --spec docs/tech-specs/milestones/milestone-M0.1.mdx
  ```
---

**Validation Criteria**:
- [ ] Package.json contains required dependencies
- [ ] Package installs without errors
- [ ] Build command succeeds
- [ ] TypeScript compilation succeeds
- [ ] No TypeScript errors

**Commands to verify**:
```bash
# Add specific validation commands here based on step type
# Example: ls -la, pnpm build, npm test, etc.
```

---

### Step 10: [ ] **SC-5** Spec passes checklist lint:

**Description**: ```bash
  node scripts/spec-lint.mjs docs/tech-specs/milestones/milestone-M0.1.mdx
  ```
- [ ] **SC-6** Agent dry-run passes:
  ```bash
  pnpm run agent:dry-run --spec docs/tech-specs/milestones/milestone-M0.1.mdx
  ```
---

**Validation Criteria**:
- [ ] Package.json contains required dependencies
- [ ] Package installs without errors
- [ ] Build command succeeds
- [ ] TypeScript compilation succeeds
- [ ] No TypeScript errors

**Commands to verify**:
```bash
# Add specific validation commands here based on step type
# Example: ls -la, pnpm build, npm test, etc.
```

---

### Step 11: [ ] **SC-6** Agent dry-run passes:

**Description**: ```bash
  pnpm run agent:dry-run --spec docs/tech-specs/milestones/milestone-M0.1.mdx
  ```
---

**Validation Criteria**:
- [ ] Package.json contains required dependencies
- [ ] Package installs without errors
- [ ] Build command succeeds

**Commands to verify**:
```bash
# Add specific validation commands here based on step type
# Example: ls -la, pnpm build, npm test, etc.
```

---

## 🎯 Final Success Criteria

After completing all steps, verify the milestone is successful by checking:

- [ ] All individual step validations pass
- [ ] Overall milestone objectives are met
- [ ] No errors in build/test processes
- [ ] All deliverables are present and functional

## 📝 Execution Notes

**Important**:
- Work through steps sequentially - do not skip ahead
- Validate each step before proceeding to the next
- Document any issues or deviations in your execution log
- If you encounter problems, refer back to the agent rules and troubleshooting guides

## 🚨 If Something Goes Wrong

1. **Check the validation criteria** - ensure all requirements are met
2. **Review agent-specific rules** - you may have missed a key requirement
3. **Consult troubleshooting documentation** in docs/tech-specs/process/
4. **Document the issue** for future improvement of these instructions

---

**Generated by**: Milestone Instruction Generator
**Last Updated**: 2025-06-01
